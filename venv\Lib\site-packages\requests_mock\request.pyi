# Stubs for requests_mock.request

from typing import Any, Dict, List

class _RequestObjectProxy:
    def __init__(self, request: Any, **kwargs: Any) -> None: ...
    def __getattr__(self, name: str) -> Any: ...
    @property
    def scheme(self) -> str: ...
    @property
    def netloc(self) -> str: ...
    @property
    def hostname(self) -> str: ...
    @property
    def port(self) -> int: ...
    @property
    def path(self) -> str: ...
    @property
    def query(self) -> str: ...
    @property
    def qs(self) -> Dict[str, List[str]]: ...
    @property
    def timeout(self) -> int: ...
    @property
    def allow_redirects(self) -> bool: ...
    @property
    def verify(self) -> Any: ...
    @property
    def stream(self) -> Any: ...
    @property
    def cert(self) -> Any: ...
    @property
    def proxies(self) -> Any: ...
    @property
    def text(self) -> str: ...
    def json(self, **kwargs: Any) -> Any: ...
    @property
    def matcher(self) -> Any: ...
    

Request = _RequestObjectProxy
