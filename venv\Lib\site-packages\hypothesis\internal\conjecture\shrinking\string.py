# This file is part of Hypothesis, which may be found at
# https://github.com/HypothesisWorks/hypothesis/
#
# Copyright the Hypothesis Authors.
# Individual contributors are listed in AUTHORS.rst and the git log.
#
# This Source Code Form is subject to the terms of the Mozilla Public License,
# v. 2.0. If a copy of the MPL was not distributed with this file, You can
# obtain one at https://mozilla.org/MPL/2.0/.

from hypothesis.internal.conjecture.shrinking.collection import Collection
from hypothesis.internal.conjecture.shrinking.integer import Integer


class String(Collection):
    def __init__(self, initial, predicate, *, intervals, **kwargs):
        super().__init__(
            list(initial),
            lambda val: predicate("".join(val)),
            to_order=intervals.index_from_char_in_shrink_order,
            from_order=intervals.char_in_shrink_order,
            ElementShrinker=Integer,
            **kwargs,
        )
