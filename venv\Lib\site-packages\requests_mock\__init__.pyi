# Stubs for requests_mock

from requests_mock.adapter import (
    <PERSON><PERSON> as <PERSON><PERSON>, 
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, 
    <PERSON><PERSON> as Callback, 
    <PERSON><PERSON><PERSON><PERSON> as <PERSON>Matcher,
)
from requests_mock.exceptions import (
    <PERSON>ck<PERSON>x<PERSON> as <PERSON>ckException, 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>ockAddress,
)
from requests_mock.mocker import (
    <PERSON><PERSON><PERSON> as DELE<PERSON>, 
    GET as GET, 
    HEAD as HEAD,
    <PERSON><PERSON> as <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
    OPTIONS as OPTIONS,
    PATCH as PATCH,
    POST as POST,
    PUT as PUT,
    mock as mock,
)
from requests_mock.request import (
    Request as Request,
    _RequestObjectProxy as _RequestObjectProxy,  # For backward compatibility
)
from requests_mock.response import (
    <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
    create_response as create_response,
    Context as Context,
)
